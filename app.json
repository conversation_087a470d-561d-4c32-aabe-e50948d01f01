{"expo": {"name": "Blinker", "slug": "blinker", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "blinker", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#03050c"}, "package": "com.mathis13.blinkerui"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "name": "Blinker"}, "plugins": ["expo-router", ["expo-splash-screen", {"backgroundColor": "#ffffff", "image": "./assets/images/splash-icon-dark.png", "dark": {"image": "./assets/images/splash-icon.png", "backgroundColor": "#03050C"}, "imageWidth": 200}], "expo-localization", ["expo-video", {"supportsBackgroundPlayback": true, "supportsPictureInPicture": true}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "cli": {"appVersionSource": "0.2"}, "eas": {"projectId": "37830f43-41c7-47ed-a264-dab7dd0aac65"}}}}