#===============================dev.blinker.eterny.fr========================================

<VirtualHost *:80>
    ServerName dev.blinker.eterny.fr

    RewriteEngine On
    # Redirect to the correct domain name
    RewriteCond %{HTTP_HOST} !^dev.blinker.eterny.fr$ [NC]
    RewriteRule ^/?(.*)$ https://dev.blinker.eterny.fr/$1 [NE,L,R=301]

</VirtualHost>

<VirtualHost *:443>
    ServerName dev.blinker.eterny.fr

    ProxyPreserveHost On
    ProxyPass / http://localhost:3011/
    ProxyPassReverse / http://localhost:3011/

    # Important for WebSocket connections
    ProxyRequests Off
    ProxyVia Off

    # Gestion des WebSockets
    RewriteEngine On
    RewriteCond %{HTTP:Upgrade} websocket [NC]
    RewriteCond %{HTTP:Connection} upgrade [NC]
    RewriteRule ^/socket\.io/(.*) ws://localhost:3011/socket.io/$1 [P,L]

    # WebSocket pour hot module reload et autres
    ProxyPass /hot ws://localhost:3011/hot
    ProxyPassReverse /hot ws://localhost:3011/hot

    ProxyPass /message ws://localhost:3011/message
    ProxyPassReverse /message ws://localhost:3011/message

    # Regular HTTP requests for socket.io
    ProxyPass /socket.io/ http://localhost:3011/socket.io/
    ProxyPassReverse /socket.io/ http://localhost:3011/socket.io/

    # Configuration CORS pour socket.io
    <Location /socket.io/>
        ProxyPreserveHost On
        RequestHeader set X-Forwarded-Proto "https"
        RequestHeader set X-Forwarded-Port "443"
        Header always set Access-Control-Allow-Origin "https://app.dev.blinker.eterny.fr"
        Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
        Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
        Header always set Access-Control-Allow-Credentials "true"
    </Location>

    SSLEngine on
    SSLProxyEngine on
    SSLProtocol -all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1 +TLSv1.2
    SSLCipherSuite HIGH:aNULL:eNULL:EXPORT:DES:RC4:!MD5:!PSK:!SRP:!CAMELLIA
    SSLCertificateFile C:\Certbot\live\dev.blinker.eterny.fr\fullchain.pem
    SSLCertificateKeyFile C:\Certbot\live\dev.blinker.eterny.fr\privkey.pem
</VirtualHost>

#===============================bdd.dev.blinker.eterny.fr==================================

<VirtualHost *:5432>
    ServerName bdd.dev.blinker.eterny.fr

    ProxyPreserveHost On
    ProxyPass / http://localhost:5432/
    ProxyPassReverse / http://localhost:5432/
</VirtualHost>

#===============================app.dev.blinker.eterny.fr==================================
<IfModule !proxy_module>
    LoadModule proxy_module modules/mod_proxy.so
</IfModule>
<IfModule !proxy_http_module>
    LoadModule proxy_http_module modules/mod_proxy_http.so
</IfModule>
<IfModule !proxy_wstunnel_module>
    LoadModule proxy_wstunnel_module modules/mod_proxy_wstunnel.so
</IfModule>

<VirtualHost *:80>
    ServerName app.dev.blinker.eterny.fr

    RewriteEngine On
    # Redirect to the correct domain name
    RewriteCond %{HTTP_HOST} !^app.dev.blinker.eterny.fr$ [NC]
    RewriteRule ^/?(.*)$ https://app.dev.blinker.eterny.fr/$1 [NE,L,R=301]

    # Redirect HTTP to HTTPS
    RewriteRule ^(.*)$ https://%{HTTP_HOST}$1 [R=301,L]
</VirtualHost>

<VirtualHost *:443>
    ServerName app.dev.blinker.eterny.fr

    # Configuration SSL
    SSLEngine on
    SSLProxyEngine on
    SSLProtocol -all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1 +TLSv1.2
    SSLCipherSuite HIGH:aNULL:eNULL:EXPORT:DES:RC4:!MD5:!PSK:!SRP:!CAMELLIA
    SSLCertificateFile C:\Certbot\live\app.dev.blinker.eterny.fr\fullchain.pem
    SSLCertificateKeyFile C:\Certbot\live\app.dev.blinker.eterny.fr\privkey.pem

    # Important for WebSocket connections
    ProxyRequests Off
    ProxyVia Off

    # WebSocket configuration for socket.io
    # First, handle WebSocket upgrade requests
    RewriteEngine On
    RewriteCond %{HTTP:Upgrade} websocket [NC]
    RewriteCond %{HTTP:Connection} upgrade [NC]
    RewriteRule ^/socket\.io/(.*) wss://dev.blinker.eterny.fr/socket.io/$1 [P,L]

    # Then handle regular HTTP requests for socket.io
    ProxyPass /socket.io/ https://dev.blinker.eterny.fr/socket.io/
    ProxyPassReverse /socket.io/ https://dev.blinker.eterny.fr/socket.io/


    # Configuration du proxy principal
    ProxyPreserveHost On
    ProxyPass / http://localhost:8081/
    ProxyPassReverse / http://localhost:8081/

    # Additional headers for WebSocket
    <Location /socket.io/>
        ProxyPreserveHost On
        RequestHeader set X-Forwarded-Proto "https"
        RequestHeader set X-Forwarded-Port "443"
        Header always set Access-Control-Allow-Origin "https://app.dev.blinker.eterny.fr"
        Header always set Access-Control-Allow-Methods "GET, POST, OPTIONS"
        Header always set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
        Header always set Access-Control-Allow-Credentials "true"
    </Location>

    # Configuration spécifique pour les uploads (utiliser HTTPS)
    ProxyPass /uploads/ https://dev.blinker.eterny.fr/uploads/ nocanon
    ProxyPassReverse /uploads/ https://dev.blinker.eterny.fr/uploads/
</VirtualHost>